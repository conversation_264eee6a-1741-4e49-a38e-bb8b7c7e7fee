@import 'tailwindcss';

/* Modern font family */
* {
	font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Custom utilities */
.line-clamp-2 {
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

/* Custom shadows */
.shadow-3xl {
	box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Smooth scrolling */
html {
	scroll-behavior: smooth;
}

/* Slide-in animations */
@keyframes slide-in-from-left {
	from {
		opacity: 0;
		transform: translateX(-20px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes slide-in-from-right {
	from {
		opacity: 0;
		transform: translateX(20px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

.animate-in {
	animation-duration: 300ms;
	animation-timing-function: ease-out;
	animation-fill-mode: both;
}

.slide-in-from-left-5 {
	animation-name: slide-in-from-left;
}

.slide-in-from-right-5 {
	animation-name: slide-in-from-right;
}

.duration-300 {
	animation-duration: 300ms;
}

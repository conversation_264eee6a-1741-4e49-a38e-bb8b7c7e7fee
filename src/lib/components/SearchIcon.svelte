<script lang="ts">
	export let onclick: (() => void) | undefined = undefined;
</script>

<!-- Search/Filter Icon -->
<button
	onclick={onclick}
	class="group w-12 h-12 bg-gradient-to-br from-blue-500 via-purple-500 to-blue-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 flex items-center justify-center border border-white/20 backdrop-blur-sm"
	aria-label="Otevřít filtry"
>
	<!-- Search icon -->
	<svg 
		class="w-6 h-6 text-white group-hover:scale-110 transition-transform duration-200" 
		fill="none" 
		stroke="currentColor" 
		viewBox="0 0 24 24"
	>
		<path 
			stroke-linecap="round" 
			stroke-linejoin="round" 
			stroke-width="2.5" 
			d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
		/>
	</svg>
</button>

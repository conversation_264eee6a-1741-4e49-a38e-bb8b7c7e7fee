# 🗺️ Mapa Brna

Interaktivní mapa užitečných míst v Brně - <PERSON><PERSON><PERSON><PERSON><PERSON> to<PERSON>, <PERSON><PERSON><PERSON><PERSON>, místa na bě<PERSON>ání, parky, obchody a více.

## 🚀 <PERSON><PERSON><PERSON><PERSON> start

```bash
# Naklonovat repozitář
git clone <repository-url>
cd mapaBrno

# Nainstalovat závislosti
npm install

# Spustit development server
npm run dev
```

Aplikace bude dostupná na http://localhost:5174

## 🛠️ Technologie

- **Frontend/Backend**: SvelteKit + TypeScript
- **Styling**: Tailwind CSS
- **Mapa**: MapBox GL JS
- **Databáze**: Supabase (PostgreSQL)
- **Hosting**: Vercel

## 📋 Funkcionality

- 🗺️ Interaktivní mapa s filtry podle kategorií
- 📍 Přidávání nových míst
- 🔍 Vyhledávání a filtry
- ⭐ Hodnocení a komentáře
- 📱 Responzivní design (mobil first)
- 🕒 Filtry podle otevírací doby

## 🎯 Kategorie míst

- 🚻 Veřejné toalety
- 📶 WiFi zdarma
- 🏃 Místa na běhání
- 🌳 Parky
- 🏪 Obchody/Večerky
- ☕ Kavárny
- 🍺 Restaurace/Bary

## ⚙️ Konfigurace

1. Zkopírujte `.env.example` do `.env`
2. Vyplňte environment variables:
   - `PUBLIC_MAPBOX_ACCESS_TOKEN` - MapBox access token
   - `PUBLIC_SUPABASE_URL` - Supabase project URL
   - `PUBLIC_SUPABASE_ANON_KEY` - Supabase anon key

## 🗄️ Databáze

Databázové schéma a testovací data najdete v složce `database/`:
- `schema.sql` - Vytvoření tabulek a nastavení
- `sample_data.sql` - Testovací data

## 📖 Dokumentace

Detailní architektura a plán vývoje je v souboru [ARCHITEKTURA.md](./ARCHITEKTURA.md).

## 🤝 Přispívání

1. Fork repozitář
2. Vytvořte feature branch (`git checkout -b feature/nova-funkcionalita`)
3. Commit změny (`git commit -am 'Přidat novou funkcionalitu'`)
4. Push do branch (`git push origin feature/nova-funkcionalita`)
5. Vytvořte Pull Request

## 📄 Licence

MIT License
